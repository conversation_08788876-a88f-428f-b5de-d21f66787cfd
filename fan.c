#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>

const char *fan_path = "/sys/class/hwmon/hwmon0/pwm1";
int main()
{
    while(1)
    {
        FILE *fd = fopen(fan_path,"w");
        if(fd == NULL)
        {
            printf("%s error\n",fan_path);
            return -1;
        }
        fprintf(fd,"%d",255);
        fclose(fd);
        sleep(5);
        fd = fopen(fan_path,"w");
        if(fd == NULL)
        {
            printf("%s error\n",fan_path);
            return -1;
        }
        fprintf(fd,"%d",0);
        fclose(fd);
        sleep(5);
    }
    return 0;
}
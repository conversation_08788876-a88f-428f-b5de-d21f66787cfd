# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.18

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: led
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/led/build && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/led -B/home/<USER>/led/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/led/build && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util

# =============================================================================
# Object build statements for EXECUTABLE target fandsys


#############################################
# Order-only phony target for fandsys

build cmake_object_order_depends_target_fandsys: phony || CMakeFiles/fandsys.dir

build CMakeFiles/fandsys.dir/fan.c.o: C_COMPILER__fandsys_Debug ../fan.c || cmake_object_order_depends_target_fandsys
  DEP_FILE = CMakeFiles/fandsys.dir/fan.c.o.d
  FLAGS = -g
  OBJECT_DIR = CMakeFiles/fandsys.dir
  OBJECT_FILE_DIR = CMakeFiles/fandsys.dir

build CMakeFiles/fandsys.dir/file.c.o: C_COMPILER__fandsys_Debug ../file.c || cmake_object_order_depends_target_fandsys
  DEP_FILE = CMakeFiles/fandsys.dir/file.c.o.d
  FLAGS = -g
  OBJECT_DIR = CMakeFiles/fandsys.dir
  OBJECT_FILE_DIR = CMakeFiles/fandsys.dir


# =============================================================================
# Link build statements for EXECUTABLE target fandsys


#############################################
# Link the executable fandsys

build fandsys: C_EXECUTABLE_LINKER__fandsys_Debug CMakeFiles/fandsys.dir/fan.c.o CMakeFiles/fandsys.dir/file.c.o
  FLAGS = -g
  OBJECT_DIR = CMakeFiles/fandsys.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = fandsys
  TARGET_PDB = fandsys.dbg

# =============================================================================
# Object build statements for EXECUTABLE target led


#############################################
# Order-only phony target for led

build cmake_object_order_depends_target_led: phony || CMakeFiles/led.dir

build CMakeFiles/led.dir/led.c.o: C_COMPILER__led_Debug ../led.c || cmake_object_order_depends_target_led
  DEP_FILE = CMakeFiles/led.dir/led.c.o.d
  FLAGS = -g
  OBJECT_DIR = CMakeFiles/led.dir
  OBJECT_FILE_DIR = CMakeFiles/led.dir

build CMakeFiles/led.dir/file.c.o: C_COMPILER__led_Debug ../file.c || cmake_object_order_depends_target_led
  DEP_FILE = CMakeFiles/led.dir/file.c.o.d
  FLAGS = -g
  OBJECT_DIR = CMakeFiles/led.dir
  OBJECT_FILE_DIR = CMakeFiles/led.dir


# =============================================================================
# Link build statements for EXECUTABLE target led


#############################################
# Link the executable led

build led: C_EXECUTABLE_LINKER__led_Debug CMakeFiles/led.dir/led.c.o CMakeFiles/led.dir/file.c.o
  FLAGS = -g
  OBJECT_DIR = CMakeFiles/led.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = led
  TARGET_PDB = led.dbg

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/led/build

build all: phony fandsys led

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | ../CMakeLists.txt /usr/share/cmake-3.18/Modules/CMakeCInformation.cmake /usr/share/cmake-3.18/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /usr/share/cmake-3.18/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.18/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.18/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.18/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.18/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.18/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.18/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.18/Modules/Compiler/GNU-C.cmake /usr/share/cmake-3.18/Modules/Compiler/GNU.cmake /usr/share/cmake-3.18/Modules/Internal/CMakeCheckCompilerFlag.cmake /usr/share/cmake-3.18/Modules/Platform/Linux-GNU-C.cmake /usr/share/cmake-3.18/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.18/Modules/Platform/Linux.cmake /usr/share/cmake-3.18/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.18.4/CMakeCCompiler.cmake CMakeFiles/3.18.4/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build ../CMakeLists.txt /usr/share/cmake-3.18/Modules/CMakeCInformation.cmake /usr/share/cmake-3.18/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /usr/share/cmake-3.18/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.18/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.18/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.18/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.18/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.18/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.18/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.18/Modules/Compiler/GNU-C.cmake /usr/share/cmake-3.18/Modules/Compiler/GNU.cmake /usr/share/cmake-3.18/Modules/Internal/CMakeCheckCompilerFlag.cmake /usr/share/cmake-3.18/Modules/Platform/Linux-GNU-C.cmake /usr/share/cmake-3.18/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.18/Modules/Platform/Linux.cmake /usr/share/cmake-3.18/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.18.4/CMakeCCompiler.cmake CMakeFiles/3.18.4/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all

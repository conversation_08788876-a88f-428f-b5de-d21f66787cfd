{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/3.18.4/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.18.4/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/Compiler/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/Internal/CMakeCheckCompilerFlag.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/Platform/Linux-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/Platform/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.18/Modules/CMakeCommonLanguageInclude.cmake"}], "kind": "cmakeFiles", "paths": {"build": "/home/<USER>/led/build", "source": "/home/<USER>/led"}, "version": {"major": 1, "minor": 0}}
{"configurations": [{"directories": [{"build": ".", "minimumCMakeVersion": {"string": "3.10.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "led", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "fandsys::@6890427a1f51a3e7e1df", "jsonFile": "target-fandsys-Debug-51a41a77edba8d211ff7.json", "name": "fandsys", "projectIndex": 0}, {"directoryIndex": 0, "id": "led::@6890427a1f51a3e7e1df", "jsonFile": "target-led-Debug-e59a3a4a3b1f15987a26.json", "name": "led", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/led/build", "source": "/home/<USER>/led"}, "version": {"major": 2, "minor": 1}}
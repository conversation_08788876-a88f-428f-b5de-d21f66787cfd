[{"directory": "/home/<USER>/led/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -g -o CMakeFiles/fandsys.dir/fan.c.o -c /home/<USER>/led/fan.c", "file": "/home/<USER>/led/fan.c"}, {"directory": "/home/<USER>/led/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -g -o CMakeFiles/fandsys.dir/file.c.o -c /home/<USER>/led/file.c", "file": "/home/<USER>/led/file.c"}, {"directory": "/home/<USER>/led/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -g -o CMakeFiles/led.dir/led.c.o -c /home/<USER>/led/led.c", "file": "/home/<USER>/led/led.c"}, {"directory": "/home/<USER>/led/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -g -o CMakeFiles/led.dir/file.c.o -c /home/<USER>/led/file.c", "file": "/home/<USER>/led/file.c"}]
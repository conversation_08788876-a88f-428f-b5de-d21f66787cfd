# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.18

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/led

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/led/build

# Include any dependencies generated for this target.
include CMakeFiles/led.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/led.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/led.dir/flags.make

CMakeFiles/led.dir/led.c.o: CMakeFiles/led.dir/flags.make
CMakeFiles/led.dir/led.c.o: ../led.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/led/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/led.dir/led.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/led.dir/led.c.o -c /home/<USER>/led/led.c

CMakeFiles/led.dir/led.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/led.dir/led.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/led/led.c > CMakeFiles/led.dir/led.c.i

CMakeFiles/led.dir/led.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/led.dir/led.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/led/led.c -o CMakeFiles/led.dir/led.c.s

CMakeFiles/led.dir/file.c.o: CMakeFiles/led.dir/flags.make
CMakeFiles/led.dir/file.c.o: ../file.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/led/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/led.dir/file.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/led.dir/file.c.o -c /home/<USER>/led/file.c

CMakeFiles/led.dir/file.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/led.dir/file.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/led/file.c > CMakeFiles/led.dir/file.c.i

CMakeFiles/led.dir/file.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/led.dir/file.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/led/file.c -o CMakeFiles/led.dir/file.c.s

# Object files for target led
led_OBJECTS = \
"CMakeFiles/led.dir/led.c.o" \
"CMakeFiles/led.dir/file.c.o"

# External object files for target led
led_EXTERNAL_OBJECTS =

led: CMakeFiles/led.dir/led.c.o
led: CMakeFiles/led.dir/file.c.o
led: CMakeFiles/led.dir/build.make
led: CMakeFiles/led.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/led/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking C executable led"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/led.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/led.dir/build: led

.PHONY : CMakeFiles/led.dir/build

CMakeFiles/led.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/led.dir/cmake_clean.cmake
.PHONY : CMakeFiles/led.dir/clean

CMakeFiles/led.dir/depend:
	cd /home/<USER>/led/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/led /home/<USER>/led /home/<USER>/led/build /home/<USER>/led/build /home/<USER>/led/build/CMakeFiles/led.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/led.dir/depend


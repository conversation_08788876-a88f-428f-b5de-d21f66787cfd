# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.18

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/led

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/led/build

# Include any dependencies generated for this target.
include CMakeFiles/fandsys.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/fandsys.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/fandsys.dir/flags.make

CMakeFiles/fandsys.dir/fan.c.o: CMakeFiles/fandsys.dir/flags.make
CMakeFiles/fandsys.dir/fan.c.o: ../fan.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/led/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/fandsys.dir/fan.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/fandsys.dir/fan.c.o -c /home/<USER>/led/fan.c

CMakeFiles/fandsys.dir/fan.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/fandsys.dir/fan.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/led/fan.c > CMakeFiles/fandsys.dir/fan.c.i

CMakeFiles/fandsys.dir/fan.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/fandsys.dir/fan.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/led/fan.c -o CMakeFiles/fandsys.dir/fan.c.s

CMakeFiles/fandsys.dir/file.c.o: CMakeFiles/fandsys.dir/flags.make
CMakeFiles/fandsys.dir/file.c.o: ../file.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/led/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/fandsys.dir/file.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/fandsys.dir/file.c.o -c /home/<USER>/led/file.c

CMakeFiles/fandsys.dir/file.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/fandsys.dir/file.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/led/file.c > CMakeFiles/fandsys.dir/file.c.i

CMakeFiles/fandsys.dir/file.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/fandsys.dir/file.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/led/file.c -o CMakeFiles/fandsys.dir/file.c.s

# Object files for target fandsys
fandsys_OBJECTS = \
"CMakeFiles/fandsys.dir/fan.c.o" \
"CMakeFiles/fandsys.dir/file.c.o"

# External object files for target fandsys
fandsys_EXTERNAL_OBJECTS =

fandsys: CMakeFiles/fandsys.dir/fan.c.o
fandsys: CMakeFiles/fandsys.dir/file.c.o
fandsys: CMakeFiles/fandsys.dir/build.make
fandsys: CMakeFiles/fandsys.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/led/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking C executable fandsys"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/fandsys.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/fandsys.dir/build: fandsys

.PHONY : CMakeFiles/fandsys.dir/build

CMakeFiles/fandsys.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/fandsys.dir/cmake_clean.cmake
.PHONY : CMakeFiles/fandsys.dir/clean

CMakeFiles/fandsys.dir/depend:
	cd /home/<USER>/led/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/led /home/<USER>/led /home/<USER>/led/build /home/<USER>/led/build /home/<USER>/led/build/CMakeFiles/fandsys.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/fandsys.dir/depend


#include "file.h"
#include <unistd.h>
#include <stdio.h>

int main()
{
    printf("=== 温度湿度控制系统演示 ===\n");
    printf("功能说明：\n");
    printf("1. 温度高于29°C时自动启动风扇\n");
    printf("2. 湿度高于70%%时LED闪烁警告\n");
    printf("3. 每5秒检测一次传感器数据\n\n");
    
    // 模拟不同的温度和湿度情况
    float scenarios[][2] = {
        {25.5, 65.0},  // 正常情况
        {30.2, 68.0},  // 温度过高
        {28.0, 75.0},  // 湿度过高
        {31.5, 80.0},  // 温度和湿度都过高
        {26.0, 60.0}   // 恢复正常
    };
    
    const char* descriptions[] = {
        "正常情况 - 温度和湿度都在正常范围",
        "温度过高 - 启动风扇降温",
        "湿度过高 - LED闪烁警告",
        "温度湿度都过高 - 风扇启动且LED闪烁",
        "恢复正常 - 关闭风扇"
    };
    
    int fan_running = 0;
    
    for(int i = 0; i < 5; i++)
    {
        float temp = scenarios[i][0];
        float humidity = scenarios[i][1];
        
        printf("=== 场景 %d: %s ===\n", i+1, descriptions[i]);
        printf("当前温度: %.1f°C\n", temp);
        printf("当前湿度: %.1f%%\n", humidity);
        
        // 温度控制逻辑
        if(temp > 29.0 && !fan_running)
        {
            printf("🌡️  温度过高！启动风扇...\n");
            printf("风扇状态: 开启 (速度: 255)\n");
            fan_running = 1;
        }
        else if(temp <= 29.0 && fan_running)
        {
            printf("🌡️  温度正常，关闭风扇...\n");
            printf("风扇状态: 关闭\n");
            fan_running = 0;
        }
        else if(fan_running)
        {
            printf("风扇状态: 运行中\n");
        }
        else
        {
            printf("风扇状态: 关闭\n");
        }
        
        // 湿度控制逻辑
        if(humidity > 70.0)
        {
            printf("💧 湿度过高！LED警告闪烁...\n");
            printf("LED状态: 闪烁3次\n");
            // 模拟LED闪烁
            for(int j = 0; j < 3; j++)
            {
                printf("💡 LED亮 -> ");
                fflush(stdout);
                usleep(300000);
                printf("LED灭\n");
                usleep(300000);
            }
        }
        else
        {
            printf("LED状态: 正常\n");
        }
        
        printf("-------------------\n");
        sleep(2); // 等待2秒进入下一个场景
    }
    
    printf("\n演示完成！\n");
    printf("实际使用时，程序会持续监控传感器数据并自动控制设备。\n");
    
    return 0;
}

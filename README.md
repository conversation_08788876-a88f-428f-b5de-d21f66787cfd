# 温湿度智能控制系统

## 系统功能

这是一个基于Linux系统的智能环境控制系统，具有以下功能：

### 🌡️ 温度控制
- **监控路径**: `/sys/bus/iio/devices/iio:device0/`
- **阈值**: 29°C
- **动作**: 温度超过29°C时自动启动风扇
- **计算公式**: `temperature = (raw + offset) * scale / 1000`

### 💧 湿度监控  
- **监控路径**: `/sys/bus/iio/devices/iio:device0/in_humidityrelative_raw`
- **阈值**: 70%
- **动作**: 湿度超过70%时LED闪烁警告

### 🌀 风扇控制
- **控制路径**: `/sys/class/hwmon/hwmon0/pwm1`
- **速度范围**: 0-255
- **控制逻辑**: 温度过高时启动，温度正常时关闭

### 💡 LED指示
- **控制路径**: 
  - `/sys/class/leds/led1/brightness`
  - `/sys/class/leds/led2/brightness`
  - `/sys/class/leds/led3/brightness`
- **警告模式**: 湿度过高时闪烁3次

## 文件结构

```
led/
├── led.c          # 主控制程序
├── file.c         # 设备控制函数
├── file.h         # 头文件
├── demo.c         # 演示程序
├── fan.c          # 风扇测试程序
├── CMakeLists.txt # 构建配置
└── build/         # 编译输出目录
```

## 编译和运行

### 编译
```bash
cd build
make
```

### 运行主程序
```bash
./led
```

### 运行演示程序
```bash
./demo
```

## 系统参数

| 参数 | 值 | 说明 |
|------|----|----|
| 温度阈值 | 29°C | 超过此温度启动风扇 |
| 湿度阈值 | 70% | 超过此湿度LED闪烁 |
| 检测频率 | 5秒 | 传感器数据检测间隔 |
| 风扇最大速度 | 255 | PWM最大值 |
| LED闪烁次数 | 3次 | 湿度警告闪烁次数 |

## 传感器路径说明

### 温度传感器 (IIO设备)
- `in_temp_raw`: 原始温度值
- `in_temp_offset`: 温度偏移量
- `in_temp_scale`: 温度缩放因子

### 湿度传感器 (IIO设备)
- `in_humidityrelative_raw`: 原始湿度值

## 使用说明

1. 确保系统具有相应的传感器和执行器
2. 检查设备路径是否存在
3. 确保程序具有读写设备文件的权限
4. 运行程序开始自动监控和控制

## 注意事项

- 程序需要适当的系统权限来访问设备文件
- 传感器路径可能因硬件平台而异
- 湿度转换公式可能需要根据具体传感器调整
- 建议在实际部署前进行充分测试

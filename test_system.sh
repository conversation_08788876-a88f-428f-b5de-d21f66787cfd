#!/bin/bash

echo "温度湿度控制系统测试脚本"
echo "=========================="

# 创建模拟的传感器数据目录（如果不存在）
sudo mkdir -p /sys/class/thermal/thermal_zone0
sudo mkdir -p /sys/class/hwmon/hwmon0
sudo mkdir -p /sys/class/hwmon/hwmon1
sudo mkdir -p /sys/class/leds/led1
sudo mkdir -p /sys/class/leds/led2
sudo mkdir -p /sys/class/leds/led3

# 创建模拟的传感器文件
echo "30000" | sudo tee /sys/class/thermal/thermal_zone0/temp > /dev/null  # 30°C
echo "75.0" | sudo tee /sys/class/hwmon/hwmon1/humidity1_input > /dev/null  # 75% 湿度

# 创建风扇和LED控制文件
sudo touch /sys/class/hwmon/hwmon0/pwm1
sudo touch /sys/class/leds/led1/brightness
sudo touch /sys/class/leds/led2/brightness
sudo touch /sys/class/leds/led3/brightness

# 设置权限
sudo chmod 666 /sys/class/thermal/thermal_zone0/temp
sudo chmod 666 /sys/class/hwmon/hwmon1/humidity1_input
sudo chmod 666 /sys/class/hwmon/hwmon0/pwm1
sudo chmod 666 /sys/class/leds/led1/brightness
sudo chmod 666 /sys/class/leds/led2/brightness
sudo chmod 666 /sys/class/leds/led3/brightness

echo "模拟传感器数据已设置："
echo "温度: 30°C (高于29°C阈值，应该启动风扇)"
echo "湿度: 75% (高于70%阈值，LED应该闪烁)"
echo ""
echo "运行温度湿度控制系统..."
echo "按 Ctrl+C 停止程序"
echo ""

# 运行控制系统
./led

#include "file.h"

// 系统路径定义（在头文件中声明，这里定义）
const char *led1_path = "/sys/class/leds/led1/brightness";
const char *led2_path = "/sys/class/leds/led2/brightness";
const char *led3_path = "/sys/class/leds/led3/brightness";

int write_data(const char *path,const char *data)
{
    FILE *fd = fopen(path,"w");
    if(fd == NULL)
    {
        printf("%s error\n",path);
        return -1;
    }
    fprintf(fd,"%s",data);
    fclose(fd);
    return 0;
}

int read_data(const char *path,float *data)
{
    FILE *fd = fopen(path,"r");
    if(fd == NULL)
    {
        printf("%s error\n",path);
        return -1;
    }
    fscanf(fd,"%f",data);
    fclose(fd);
    return 0;
}

// 控制风扇函数
int control_fan(int speed)
{
    const char *fan_path = "/sys/class/hwmon/hwmon0/pwm1";
    char speed_str[10];
    sprintf(speed_str, "%d", speed);
    return write_data(fan_path, speed_str);
}

// LED闪烁函数
void led_blink(int times)
{
    for(int i = 0; i < times; i++)
    {
        // 打开所有LED
        write_data(led1_path, "1");
        write_data(led2_path, "1");
        write_data(led3_path, "1");
        usleep(500000); // 0.5秒

        // 关闭所有LED
        write_data(led1_path, "0");
        write_data(led2_path, "0");
        write_data(led3_path, "0");
        usleep(500000); // 0.5秒
    }
}

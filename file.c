#include "file.h"

int write_data(const char *path,const char *data)
{
    FILE *fd = fopen(path,"w");
    if(fd == NULL)
    {
        printf("%s error\n",path);
        return -1;
    }
    fprintf(fd,"%s",data);
    fclose(fd);
    return 0;
}

int read_data(const char *path,float *data)
{
    FILE *fd = fopen(path,"r");
    if(fd == NULL)
    {
        printf("%s error\n",path);
        return -1;
    }
    fscanf(fd,"%f",data);
    fclose(fd);
    return 0;
}

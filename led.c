#include "file.h"
#include <unistd.h>
#include <stdio.h>

// 系统路径定义 - 根据实际硬件路径
const char *temp_path = "/sys/bus/iio/devices/iio:device0/in_temp_offset";
const char *humidity_path = "/sys/bus/iio/devices/iio:device0/in_humidityrelative_raw";

// 阈值设置
#define TEMP_THRESHOLD 29.0    // 温度阈值（摄氏度）
#define HUMIDITY_THRESHOLD 70.0 // 湿度阈值（百分比）
#define FAN_SPEED_HIGH 255     // 风扇高速
#define FAN_SPEED_OFF 0        // 风扇关闭



int main()
{
    float temperature = 0.0;
    float humidity = 0.0;
    int fan_running = 0;

    printf("温度湿度控制系统启动...\n");
    printf("温度阈值: %.1f°C\n", TEMP_THRESHOLD);
    printf("湿度阈值: %.1f%%\n", HUMIDITY_THRESHOLD);

    while(1)
    {
        // 读取温度 - 使用 IIO 设备的计算方式
        float temp_offset, temp_raw, temp_scale;
        if(read_data("/sys/bus/iio/devices/iio:device0/in_temp_offset", &temp_offset) == 0 &&
           read_data("/sys/bus/iio/devices/iio:device0/in_temp_raw", &temp_raw) == 0 &&
           read_data("/sys/bus/iio/devices/iio:device0/in_temp_scale", &temp_scale) == 0)
        {
            temperature = (temp_raw + temp_offset) * temp_scale / 1000.0; // 转换为摄氏度
            printf("当前温度: %.2f°C (raw=%.0f, offset=%.0f, scale=%.6f)\n",
                   temperature, temp_raw, temp_offset, temp_scale);

            // 温度控制逻辑
            if(temperature > TEMP_THRESHOLD && !fan_running)
            {
                printf("温度过高！启动风扇...\n");
                control_fan(FAN_SPEED_HIGH);
                fan_running = 1;
            }
            else if(temperature <= TEMP_THRESHOLD && fan_running)
            {
                printf("温度正常，关闭风扇...\n");
                control_fan(FAN_SPEED_OFF);
                fan_running = 0;
            }
        }
        else
        {
            printf("读取温度失败\n");
        }

        // 读取湿度 - 使用 IIO 设备的原始值
        float humidity_raw;
        if(read_data("/sys/bus/iio/devices/iio:device0/in_humidityrelative_raw", &humidity_raw) == 0)
        {
            // 根据传感器规格转换湿度值（这里假设需要适当的转换）
            humidity = humidity_raw / 1000.0; // 可能需要根据实际传感器调整转换公式
            printf("当前湿度: %.2f%% (raw=%.0f)\n", humidity, humidity_raw);

            // 湿度控制逻辑
            if(humidity > HUMIDITY_THRESHOLD)
            {
                printf("湿度过高！LED警告闪烁...\n");
                led_blink(3); // 闪烁3次
            }
        }
        else
        {
            printf("读取湿度失败\n");
        }

        printf("-------------------\n");
        sleep(5); // 每5秒检测一次
    }

    return 0;
}